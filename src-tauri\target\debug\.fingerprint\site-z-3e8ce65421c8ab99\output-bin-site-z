{"$message_type":"diagnostic","message":"unused import: `Listener`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":1429,"byte_end":1437,"line_start":19,"line_end":19,"column_start":42,"column_end":50,"is_primary":true,"text":[{"text":"use tauri::{App<PERSON><PERSON><PERSON>, Emitter, Runtime, Listener};","highlight_start":42,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":1427,"byte_end":1437,"line_start":19,"line_end":19,"column_start":40,"column_end":50,"is_primary":true,"text":[{"text":"use tauri::{AppHandle, Emitter, Runtime, Listener};","highlight_start":40,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Listener`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:19:42\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tauri::{AppHandle, Emitter, Runtime, Listener};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `username`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25760,"byte_end":25768,"line_start":412,"line_end":412,"column_start":56,"column_end":64,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":56,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25760,"byte_end":25768,"line_start":412,"line_end":412,"column_start":56,"column_end":64,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":56,"highlight_end":64}],"label":null,"suggested_replacement":"_username","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `username`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:412:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_username`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `password`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25778,"byte_end":25786,"line_start":412,"line_end":412,"column_start":74,"column_end":82,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":74,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":25778,"byte_end":25786,"line_start":412,"line_end":412,"column_start":74,"column_end":82,"is_primary":true,"text":[{"text":"    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {","highlight_start":74,"highlight_end":82}],"label":null,"suggested_replacement":"_password","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `password`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:412:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn login(&self, addr: String, port: u16, username: String, password: String) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_password`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot add `std::string::String` to `&str`","code":{"code":"E0369","explanation":"A binary operation was attempted on a type which doesn't support it.\n\nErroneous code example:\n\n```compile_fail,E0369\nlet x = 12f32; // error: binary operation `<<` cannot be applied to\n               //        type `f32`\n\nx << 2;\n```\n\nTo fix this error, please check that this type implements this binary\noperation. Example:\n\n```\nlet x = 12u32; // the `u32` type does implement it:\n               // https://doc.rust-lang.org/stable/std/ops/trait.Shl.html\n\nx << 2; // ok!\n```\n\nIt is also possible to overload most operators for your own type by\nimplementing traits from `std::ops`.\n\nString concatenation appends the string on the right to the string on the\nleft and may require reallocation. This requires ownership of the string\non the left. If something should be added to a string literal, move the\nliteral to the heap by allocating it with `to_owned()` like in\n`\"Your text\".to_owned()`.\n"},"level":"error","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":26216,"byte_end":26219,"line_start":425,"line_end":425,"column_start":20,"column_end":23,"is_primary":false,"text":[{"text":"        let data = \"[\" + board_id.to_owned() + \"]\" + data_hex;","highlight_start":20,"highlight_end":23}],"label":"&str","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\tcp_manager.rs","byte_start":26222,"byte_end":26241,"line_start":425,"line_end":425,"column_start":26,"column_end":45,"is_primary":false,"text":[{"text":"        let data = \"[\" + board_id.to_owned() + \"]\" + data_hex;","highlight_start":26,"highlight_end":45}],"label":"std::string::String","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\tcp_manager.rs","byte_start":26220,"byte_end":26221,"line_start":425,"line_end":425,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        let data = \"[\" + board_id.to_owned() + \"]\" + data_hex;","highlight_start":24,"highlight_end":25}],"label":"`+` cannot be used to concatenate a `&str` with a `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"create an owned `String` on the left and add a borrow on the right","code":null,"level":"help","spans":[{"file_name":"src\\core\\tcp_manager.rs","byte_start":26219,"byte_end":26219,"line_start":425,"line_end":425,"column_start":23,"column_end":23,"is_primary":true,"text":[{"text":"        let data = \"[\" + board_id.to_owned() + \"]\" + data_hex;","highlight_start":23,"highlight_end":23}],"label":null,"suggested_replacement":".to_owned()","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\core\\tcp_manager.rs","byte_start":26222,"byte_end":26222,"line_start":425,"line_end":425,"column_start":26,"column_end":26,"is_primary":true,"text":[{"text":"        let data = \"[\" + board_id.to_owned() + \"]\" + data_hex;","highlight_start":26,"highlight_end":26}],"label":null,"suggested_replacement":"&","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0369]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: cannot add `std::string::String` to `&str`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\tcp_manager.rs:425:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m425\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let data = \"[\" + board_id.to_owned() + \"]\" + data_hex;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mstd::string::String\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`+` cannot be used to concatenate a `&str` with a `String`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m&str\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: create an owned `String` on the left and add a borrow on the right\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m425\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m        let data = \"[\"\u001b[0m\u001b[0m\u001b[38;5;10m.to_owned()\u001b[0m\u001b[0m + \u001b[0m\u001b[0m\u001b[38;5;10m&\u001b[0m\u001b[0mboard_id.to_owned() + \"]\" + data_hex;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 3 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error; 3 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0369`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0369`.\u001b[0m\n"}
